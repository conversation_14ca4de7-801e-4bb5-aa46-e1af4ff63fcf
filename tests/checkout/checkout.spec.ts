import { expect, test } from "@playwright/test";
import { exec } from "child_process";
import { promisify } from "util";
import { config } from "../global";
import {
  addItemToCart,
  fillCardInfo,
  fullyCompleteForm,
  retryFetchingEmails,
} from "./checkout";

const execAsync = promisify(exec);
const MAILCRAB_CONTAINER_NAME = `mailcrab-test-${Date.now()}`;

test.describe("Checkout", () => {
  test.beforeEach(async ({ page, isMobile }) => {
    await page.goto(config("1").baseUrl);
    await addItemToCart(page, isMobile);
  });

  test.beforeAll(async () => {
    console.log("Running 'beforeAll'");
    try {
      // First check if container exists
      const { stdout } = await execAsync(
        `docker ps -a --filter "name=${MAILCRAB_CONTAINER_NAME}" --format "{{.ID}}"`,
      );

      if (stdout.trim()) {
        // Container exists, try to remove it
        await execAsync(`docker rm -f ${MAILCRAB_CONTAINER_NAME}`);
      }

      // Now create the new container
      await execAsync(
        `docker run -d --name ${MAILCRAB_CONTAINER_NAME} -p 1080:1080 -p 1025:1025 marlonb/mailcrab:latest`,
      );
    } catch (error) {
      console.error("Failed to start MailCrab container:", error);
      // Handle the error appropriately
    }
  });

  test.afterAll(async () => {
    try {
      await execAsync(`docker rm -f ${MAILCRAB_CONTAINER_NAME} || true`);
    } catch (error) {
      console.error("Failed to stop MailCrab container:", error);
      return;
    }
  });

  test("should send email after successful checkout", async ({ page }) => {
    await page.getByRole("button", { name: "Checkout" }).click();

    await fullyCompleteForm(page);

    await page.getByRole("button", { name: "Complete Order" }).click();

    const messages = await retryFetchingEmails();
    expect(messages.length).toBeGreaterThan(0);
  });

  test("should not proceed to the next step with incomplete card info", async ({
    page,
  }) => {
    await page.getByRole("button", { name: "Checkout" }).click();

    const continueButton = page.getByRole("button", { name: "Continue" });
    await expect(continueButton).toBeVisible();
    await continueButton.click();

    await expect(
      page.getByRole("button", { name: "Complete Order" }),
    ).not.toBeVisible();
  });

  test("should proceed to the next step with valid card info", async ({
    page,
  }) => {
    await page.getByRole("button", { name: "Checkout" }).click();

    await fillCardInfo(page);

    // Click continue button
    const continueButton = page.getByRole("button", { name: "Continue" });
    await continueButton.click();

    await expect(page.getByText("Your Information")).toBeVisible();
    await expect(page.getByLabel("First Name")).toBeVisible();
  });

  test("clicking on the back button should take you back to the menu", async ({
    page,
  }) => {
    await page.getByRole("button", { name: "Checkout" }).click();

    await page.getByRole("button", { name: "Back to menu" }).click();

    await expect(
      page.getByRole("heading", { name: "Categories" }),
    ).toBeVisible();
  });

  test("should show tooltip when hovering over Additional Fees info icon", async ({
    page,
    isMobile,
  }) => {
    if (isMobile) {
      return;
    }

    await page.getByRole("button", { name: "Checkout" }).click();

    const extraFeesSection = page.getByText("Additional Fees:");
    await expect(extraFeesSection).toBeVisible();

    const icon = page.locator(".tooltip-trigger:has(.info-icon)");
    await icon.scrollIntoViewIfNeeded();
    await expect(icon).toBeVisible();
    await icon.hover();
    await expect(page.getByText("Extra fees")).toBeVisible();

    await page.mouse.move(0, 0);
    await expect(
      page.getByText("Extra fees applied to takeout orders."),
    ).not.toBeVisible();
  });

  test("should show drawer when clicking on info icon", async ({
    page,
    isMobile,
  }) => {
    if (!isMobile) {
      return;
    }

    await page.getByRole("button", { name: "Checkout" }).click();
    const extraFeesSection = page.getByText("Additional Fees:");
    await expect(extraFeesSection).toBeVisible();

    const infoIcon = page.getByLabel("Additional fees information");
    await infoIcon.click();
    await expect(
      page.getByText("Additional fees are applied to takeout orders."),
    ).toBeVisible();
  });

  // test("should take you to a thank you page with order time after successful checkout", async ({
  //   page,
  // }) => {
  //   await page.getByRole("button", { name: "Checkout" }).click();
  //
  //   await fullyCompleteForm(page);
  //
  //   const pickupTime = await page.getByLabel("Pickup Time").inputValue();
  //
  //   await page.getByRole("button", { name: "Complete Order" }).click();
  //
  //   await expect(page.getByText("Thank You for Your Order!")).toBeVisible();
  //
  //   const pickupTimeElement = await page
  //     .getByText("Pickup Time")
  //     .locator("span");
  //   const thankYouPagePickupTime = await pickupTimeElement.textContent();
  //
  //   await expect(thankYouPagePickupTime).toBe(pickupTime);
  // });
});
