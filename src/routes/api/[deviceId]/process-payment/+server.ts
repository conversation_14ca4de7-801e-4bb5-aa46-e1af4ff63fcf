import type { Request<PERSON><PERSON><PERSON> } from "@sveltejs/kit";
import { error, json } from "@sveltejs/kit";
import type { processPayment } from "$lib/api/process-payment";
import { emailReceipt } from "$lib/api/email-receipt.server.ts";
import { processNmiPayment } from "$lib/api/nmi";
import { logger } from "$lib/logger/logger.svelte.ts";
import {
  OrderState,
  OrderStateTracker,
} from "$lib/services/OrderStateTracker.server.ts";
import { submitOrder } from "$lib/api/submit-order";

export const POST: RequestHandler = async ({
  request,
  fetch,
  params,
  locals,
}) => {
  try {
    if (!params.deviceId) {
      error(400, "Missing device id");
    }

    const { nmiToken, orderInfo } = (await request.json()) as Parameters<
      typeof processPayment
    >[number];

    const parsedResult = await processNmiPayment({
      nmiToken,
      orderInfo,
      fetch,
      deviceId: params.deviceId,
    });

    await submitOrder({
      fetch,
      orderInfo,
      nmiDetails: parsedResult,
      deviceId: params.deviceId,
      bridgeApiToken: locals.bridgeApiToken || "",
    });

    await emailReceipt({
      ...orderInfo,
      ...parsedResult,
    });

    const orderStateTracker = await OrderStateTracker.instance(orderInfo);
    await orderStateTracker.updateState(OrderState.COMPLETED);

    return json({
      ok: true,
      transactionId: parsedResult.transactionid,
    });
  } catch (error) {
    logger.error({ error }, "Error in process-payment");
    return json({
      ok: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
