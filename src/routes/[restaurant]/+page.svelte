<script lang="ts">
  import { browser } from "$app/environment";
  import AddItemDrawer, {
    openAddItemDrawer,
  } from "$lib/components/AddItemDrawer.svelte";
  import Cart from "$lib/components/Cart.svelte";
  import CartButton from "$lib/components/CartButton.svelte";
  import CategoriesBar from "$lib/components/CategoriesBar.svelte";
  import CategoryDrawer from "$lib/components/CategoryDrawer.svelte";
  import DepartmentHeader from "$lib/components/DepartmentHeader.svelte";
  import Drawer from "$lib/components/Drawer.svelte";
  import ItemCard from "$lib/components/ItemCard.svelte";
  import ItemView from "$lib/components/ItemView.svelte";
  import LargeScreenCart from "$lib/components/LargeScreenCart.svelte";
  import Modal from "$lib/components/Modal.svelte";
  import { hasModifiers } from "$lib/helpers/item.ts";
  import { getCartState } from "$lib/services/Cart.svelte.js";
  import { getHealthCheckService } from "$lib/services/HealthCheckService.svelte.ts";
  import { onMount, setContext } from "svelte";

  const cart = getCartState();
  const healthCheckService = getHealthCheckService();
  const { data } = $props();
  const { departments, items } = data.restaurantInfo;
  const updateIsMobile = () => {
    isMobile = window.innerWidth <= 768;
  };

  let searchInput = $state<HTMLInputElement>();
  let addItemModal: ReturnType<typeof Modal> | undefined = $state(undefined);
  let drawerInstance: ReturnType<typeof Drawer> | undefined = $state(undefined);
  let isMobile = $state(false);
  let showSearch = $state(false);
  let searchTerm = $state("");

  let filteredItems = $derived(
    items.filter(
      (item) =>
        item.item.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.desc?.toLowerCase() ?? "").includes(searchTerm.toLowerCase()),
    ),
  );
  let filteredItemsCount = $derived(filteredItems.length);
  let isCartVisible = $derived(cart.totalItemsCount > 0);
  let stickyHeader: HTMLElement | undefined = $state(undefined);
  // eslint-disable-next-line svelte/valid-compile
  let previousCount = filteredItemsCount;

  let filteredDepartments = $derived(
    searchTerm.trim() === ""
      ? departments
      : departments.filter((dept) =>
          filteredItems.some((item) => item.department === dept.department),
        ),
  );

  $effect(() => {
    if (filteredItemsCount !== previousCount) {
      previousCount = filteredItemsCount;
      vocalizeSearchResult();
    }
  });

  $effect(() => {
    setContext("modal", addItemModal);
  });

  $effect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (
        showSearch &&
        searchInput &&
        !searchInput.contains(e.target as Node)
      ) {
        showSearch = false;
      }
    };

    if (showSearch) {
      window.addEventListener("click", handleClickOutside);
    }
    return () => {
      window.removeEventListener("click", handleClickOutside);
    };
  });

  $effect(() => {
    if (cart.totalItemsCount === 0) {
      drawerInstance?.close();
    }
  });

  $effect(() => {
    updateIsMobile();
    window.addEventListener("resize", updateIsMobile);
    return () => {
      window.removeEventListener("resize", updateIsMobile);
    };
  });

  $effect(() => {
    localStorage.setItem("cart", JSON.stringify(cart.cartItems));
  });

  const vocalizeSearchResult = () => {
    const liveRegion = document.getElementById("search-results-count");
    if (!liveRegion) return;
    liveRegion.textContent =
      filteredItemsCount === 1
        ? "1 item found"
        : `${filteredItemsCount} items found`;
  };

  onMount(() => {
    isMobile = window.innerWidth <= 768;
  });
</script>

<div class="main">
  <div id="search-results-count" aria-live="polite" class="sr-only"></div>

  <section
    class="categories-bar-wrapper"
    class:has-offline-banner={!healthCheckService?.online}
    bind:this={stickyHeader}
  >
    <CategoriesBar
      bind:searchTerm
      bind:showSearch
      departments={searchTerm.trim() === "" ? departments : filteredDepartments}
    />
  </section>

  <LargeScreenCart />

  <section aria-label="Items">
    {#each filteredDepartments as department (department.department)}
      <section class="department-item-group">
        <DepartmentHeader
          name={department.title}
          stickyHeaderHeight={Number(
            browser && stickyHeader
              ? getComputedStyle(stickyHeader)
                  .getPropertyValue("--sticky-header-height")
                  .replaceAll(/[^\d]/g, "")
              : 0,
          )}
        />
        <div class="item-grid">
          {#each filteredItems.filter(({ department: currDepartment }) => currDepartment === department.department) as item (item.item)}
            {#snippet modalContent()}
              <ItemView
                {item}
                isOnline={healthCheckService.online}
                onAddToCart={() => {
                  addItemModal?.closeModal();
                }}
                onDetailedItemLoad={(detailedItem) => {
                  addItemModal?.setModalHeight(
                    hasModifiers(detailedItem) ? "lg" : "sm",
                  );
                }}
              />
            {/snippet}

            <ItemCard
              onclick={() => {
                const isInStock = item.count !== 0;
                if (isInStock) {
                  if (!isMobile) {
                    addItemModal?.open({
                      title: item.desc,
                      description: item.detailedDesc,
                      children: modalContent,
                    });
                  } else {
                    openAddItemDrawer(item);
                  }
                }
              }}
              price={item.price}
              description={item.detailedDesc}
              name={item.desc}
              highlightTerm={searchTerm}
              isInStock={item.count !== 0}
            />
          {/each}
        </div>
      </section>
    {/each}
  </section>

  <Modal bind:this={addItemModal}></Modal>

  {#if healthCheckService?.online}
    <Drawer bind:this={drawerInstance} title="Cart">
      {#if isCartVisible}
        <Cart />
      {/if}
    </Drawer>
  {/if}
</div>

<CategoryDrawer
  departments={searchTerm.trim() === "" ? departments : filteredDepartments}
/>

<AddItemDrawer />

{#if healthCheckService?.online && cart.totalItemsCount > 0}
  <CartButton onclick={() => drawerInstance?.open()} />
{/if}

<style>
  .categories-bar-wrapper {
    position: sticky;
    top: 0;
    border-bottom: 1px solid var(--gray-200);
    background-color: var(--main-bg-color);
  }

  .department-item-group {
    padding-block: var(--padding-sm);
  }

  .item-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--padding-sm);
    padding: var(--padding-md);
    width: 100%;
  }

  @media (min-width: 768px) {
    .item-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 1024px) {
    .item-grid {
      grid-template-columns: repeat(3, 1fr);
    }

    .main {
      width: calc(100% - var(--large-screen-cart-width));
    }
  }

  @media (min-width: 1440px) {
    .item-grid {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  :global(mark) {
    background-color: yellow;
    color: black;
  }
</style>
