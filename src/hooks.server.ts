import { logger } from "$lib/logger/logger.svelte.ts";
import { error as svelteKitError, type Handle, type RequestEvent } from "@sveltejs/kit";
import {
  browserRateLimiter,
  ipRateLimiter,
} from "$lib/services/RateLimiter.ts";
import { publicConfig } from "./publicConfig.ts";
import { getOrCreateBridgeApiToken } from "$lib/api/auth.server.ts";

const rateLimit = async (event: RequestEvent) => {
  const [
    { exceedsLimit: ipExceedsLimit },
    { exceedsLimit: browserExceedsLimit },
  ] = await Promise.all([
    ipRateLimiter.checkAndIncrement(event),
    browserRateLimiter.checkAndIncrement(event),
  ]);

  if (ipExceedsLimit || browserExceedsLimit) {
    logger.warn(
      { ip: event.request.headers.get("x-real-ip"), url: event.url.pathname },
      "Rate limit exceeded",
    );
    svelteKitError(429, "Too many requests");
  }
};

/**
 * @description This function runs every time the SvelteKit server receives a request.
 * @param event - The request event object representing the incoming request.
 * @param resolve - The resolve function that will be used to generate a response.
 * @returns The response object.
 */
export const handle: Handle = async ({ event, resolve }) => {
  logger.info(
    {
      method: event.request.method,
      url: event.url.pathname,
      ip: event.request.headers.get("x-real-ip"),
    },
    "Handling request",
  );

  if (
    event.url.pathname.startsWith("/api/bridge") ||
    event.url.pathname.includes("/process-payment")
  ) {
    event.locals.bridgeApiToken = await getOrCreateBridgeApiToken();
  }

  if (publicConfig.env !== "dev" && publicConfig.env !== "staging") {
    try {
      await rateLimit(event);
    } catch (err) {
      logger.error(err, "Rate limit exceeded");
      svelteKitError(429, "Too many requests");
    }
  }

  const response = await resolve(event);
  if (response.status >= 400) {
    logger.error(
      { status: response.status, url: event.url.pathname },
      "Request resolved with error",
    );
  } else {
    logger.info(
      { status: response.status, url: event.url.pathname },
      "Request resolved",
    );
  }
  return response;
};
