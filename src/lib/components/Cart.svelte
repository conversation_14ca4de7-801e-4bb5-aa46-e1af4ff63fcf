<script lang="ts">
  import { goto } from "$app/navigation";
  import { getCartState } from "$lib/services/Cart.svelte";
  import { centToDollar } from "$lib/helpers/number.ts";
  import { routes } from "$lib/helpers/routes.ts";
  import CartItems from "./CartItems.svelte";
  import { getHealthCheckService } from "$lib/services/HealthCheckService.svelte.ts";
  import { page } from "$app/stores";

  const handleCheckout = () => {
    if (!$page.params.restaurant) return;
    goto(routes.checkout($page.params.restaurant));
  };

  const cart = getCartState();
  const healthCheckService = getHealthCheckService();
  const online = $derived(healthCheckService.online);
</script>

<section class="cart-container" aria-label="Cart">
  <CartItems />
  <section aria-label="Cart Footer" class="cart-footer">
    <div class="total">
      Subtotal: <span class="total-amount">{centToDollar(cart.subTotal)}</span>
    </div>
    <button
      class="button--primary-lg"
      disabled={!online}
      onclick={handleCheckout}
    >
      Checkout
    </button>
  </section>
</section>

<style>
  .cart-container {
    --cart-footer-height: 6rem;

    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .cart-footer {
    flex-shrink: 0;
    border-top: 1px solid var(--gray-200);
    background-color: white;
    padding: var(--padding-sm);
  }

  @media (min-width: 1024px) {
    .cart-container {
      height: calc(100vh - var(--cart-footer-height));
      overflow-y: scroll;
    }

    .cart-footer {
      position: fixed;
      right: 0;
      bottom: 0;
      left: calc(100vw - (var(--large-screen-cart-width) - 1px));
      padding: var(--padding-md);
      height: var(--cart-footer-height);
    }
  }

  .total {
    font-weight: bold;
    font-size: 1.2em;
  }

  .total-amount {
    color: var(--primary-color);
  }

  .cart-footer button {
    margin-top: 2px;
    border: none;
    border-radius: 5px;
    background-color: var(--primary-color);
    padding: 10px;
    width: 100%;
    color: white;
    font-size: 16px;
  }

  .cart-footer button:hover {
    background-color: var(--primary-color);
  }
</style>
