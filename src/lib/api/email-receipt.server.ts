import type { ReceiptEmailInfo } from "$lib/types";
import { toast } from "$lib/services/ToastManager.svelte.ts";
import { generateCartItemsHtmlRows } from "$lib/helpers/generate-receipt-cart-items.ts";
import { CheckoutError } from "$lib/services/errors/checkout/CheckoutError.ts";
import { sendEmail } from "$lib/api/emailer.server.ts";
import { centToDollar } from "$lib/helpers/number.ts";

export const emailReceipt = async (receiptEmailInfo: ReceiptEmailInfo) => {
  const {
    cart,
    promisedTime,
    customer,
    paymentMethod,
    storeInfo,
    transactionid,
    card,
  } = receiptEmailInfo;

  if (!cart.items || cart.items.length === 0 || cart.totalItemsCount === 0) {
    toast.error("Cart is empty or invalid");
    throw new CheckoutError({
      name: "EMAIL_SENDING_ERROR",
      message: "Cart is empty or invalid",
    });
  }

  if (!customer.email || !customer.firstName || !customer.lastName) {
    toast.error("Customer email and name are required");
    throw new CheckoutError({
      name: "EMAIL_SENDING_ERROR",
      message: "Customer email and name are required",
    });
  }

  if (paymentMethod !== "credit-card") {
    throw new CheckoutError({
      name: "EMAIL_SENDING_ERROR",
      message: "Only credit card payments are currently supported",
    });
  }

  const cartItemsHtml = generateCartItemsHtmlRows(cart.items);

  if (!storeInfo.name) {
    throw new CheckoutError({
      name: "EMAIL_SENDING_ERROR",
      message: "Failed to find merchant details for email",
      cause: {
        merchantName: storeInfo.name,
        merchantPhone: storeInfo.phone || "",
      },
    });
  }

  await sendEmail({
    to: customer.email,
    subject: "Order Confirmation",
    templateName: "order-receipt",
    variables: {
      dynamicContent: cartItemsHtml,
      total: centToDollar(cart.subTotal),
      cartTip: `$${centToDollar(cart.tip)}`,
      salesTax: `$${cart.tax}`,
      grandTotal: centToDollar(cart.total),
      merchantAddress: storeInfo.address,
      merchantName: storeInfo.name,
      merchantPhone: storeInfo.phone,
      name: customer.firstName,
      invoiceNumber: transactionid,
      cardInfo: `**** **** **** ${card.pan}`,
      promisedTime: promisedTime.emailFormat,
    },
  });
};
