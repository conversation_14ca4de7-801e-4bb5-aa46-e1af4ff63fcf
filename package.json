{"name": "online-ordering", "version": "0.0.1", "private": true, "scripts": {"dev": "doppler run --project online-ordering --config dev --command='env && node --inspect node_modules/vite/bin/vite.js dev'", "build": "doppler run --project online-ordering --config ${DOPPLER_CONFIG:-dev} --command='vite build'", "start": "doppler run --project online-ordering --config ${DOPPLER_CONFIG:-dev} --command='node build/index.js'", "preview": "doppler run --project online-ordering --config ${DOPPLER_CONFIG:-dev} --command='vite preview'", "print-env": "doppler run --project online-ordering --config ${DOPPLER_CONFIG:-dev} --command='env'", "test": "doppler run --project online-ordering --config ${DOPPLER_CONFIG:-dev} --command='npm run test:integration && npm run test:unit'", "check": "doppler run --project online-ordering --config ${DOPPLER_CONFIG:-dev} --command='svelte-kit sync && svelte-check --tsconfig ./tsconfig.json'", "check:watch": "doppler run --project online-ordering --config ${DOPPLER_CONFIG:-dev} --command='svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch'", "lint": "doppler run --project online-ordering --config ${DOPPLER_CONFIG:-dev} --command='eslint .'", "playwright-ui": "doppler run --project online-ordering --config ${DOPPLER_CONFIG:-dev} --command='playwright test --ui'", "test:integration": "doppler run --project online-ordering --config ${DOPPLER_CONFIG:-dev} --command='playwright test'", "test:unit": "doppler run --project online-ordering --config ${DOPPLER_CONFIG:-dev} --command='vitest'", "email-test": "doppler run --project online-ordering --config ${DOPPLER_CONFIG:-dev} --command='docker run --rm -p 1080:1080 -p 1025:1025 marlonb/mailcrab:latest'", "dev:debug": "doppler run --project online-ordering --config dev --command='env && node --inspect-brk node_modules/vite/bin/vite.js dev'"}, "devDependencies": {"@iconify-json/mdi": "^1.1.67", "@melt-ui/pp": "^0.3.2", "@melt-ui/svelte": "^0.81.0", "@playwright/test": "^1.52.0", "@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/kit": "^2.5.28", "@sveltejs/vite-plugin-svelte": "^4.0.0-next.6", "@types/eslint": "^8.56.7", "@types/js-yaml": "^4.0.9", "@types/mjml": "^4.7.4", "@types/nodemailer": "^6.4.15", "eslint": "^9.0.0", "eslint-plugin-svelte": "^2.46.1", "globals": "^15.0.0", "prettier": "^3.3.2", "prettier-plugin-css-order": "^2.1.2", "prettier-plugin-svelte": "^3.4.0", "svelte": "^5.19.10", "svelte-check": "^4.1.4", "tslib": "^2.4.1", "typescript": "^5.7.3", "typescript-eslint": "^8.31.0", "unplugin-icons": "^0.19.0", "vite": "^5.4.7", "vitest": "^1.2.0"}, "type": "module", "dependencies": {"@axe-core/playwright": "^4.10.0", "@sveltejs/adapter-node": "^5.2.12", "ioredis": "^5.6.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "mjml": "^4.15.3", "nodemailer": "^6.9.14", "pino": "^9.6.0", "pino-loki": "^2.5.0", "resend": "^3.5.0", "zod": "^3.25.23"}}